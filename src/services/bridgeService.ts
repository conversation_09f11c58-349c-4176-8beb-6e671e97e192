import { parseUnits } from 'viem';
import { BridgeConfig, getBridgeConfig, getNetworkRpcUrl, getTokenDecimals, CONTRACT_ADDRESSES } from '../utils/bridgeConfig';
import { Network } from '../components/NetworkSelector';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { walletAdapterIdentity } from '@metaplex-foundation/umi-signer-wallet-adapters';
import { publicKey } from '@metaplex-foundation/umi';
import { oft } from '@layerzerolabs/oft-v2-solana-sdk';

export interface QuoteResult {
  nativeFee: string;
  receiveAmount: string;
  gasPrice?: string;
}

export interface BridgeResult {
  txHash: string;
  layerZeroScanLink?: string;
}

// Gas limits for different networks
const ETHEREUM_GAS_LIMIT = BigInt(200000);
const BASE_GAS_LIMIT = BigInt(200000);

// Utility function to convert Ethereum address to bytes32
function addressToBytes32(address: string): string {
  // Remove 0x prefix and pad to 32 bytes
  const cleanAddress = address.replace('0x', '').toLowerCase();
  return '0x' + '0'.repeat(24) + cleanAddress;
}

// Utility function to fetch gas price from a public API
async function fetchGasPrice(chainId: number = 1): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> {
  try {
    // Use a simple fallback gas price estimation
    // In production, you might want to use services like Blocknative, Etherscan API, etc.
    const baseGasPrice = chainId === 8453 ? BigInt(1 * 1e9) : BigInt(20 * 1e9); // 1 gwei for Base, 20 gwei for Ethereum
    const priorityFee = BigInt(2 * 1e9); // 2 gwei priority fee

    return {
      maxFeePerGas: baseGasPrice + priorityFee,
      maxPriorityFeePerGas: priorityFee
    };
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
}

export class BridgeService {
  private config: BridgeConfig;

  constructor(fromNetwork: Network, toNetwork: Network) {
    this.config = getBridgeConfig(fromNetwork, toNetwork);
  }

  isSupported(): boolean {
    return this.config.isSupported;
  }

  getBridgeType(): string {
    return this.config.bridgeType;
  }

  async quoteBridge(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string
  ): Promise<QuoteResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.quoteSolanaToEvm(amount, recipientAddress, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.quoteEvmToSolana(amount, recipientAddress, ethAddress || '');
      case 'EVM_TO_EVM':
        return this.quoteEvmToEvm(amount, recipientAddress, ethAddress || '');
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  async executeBridge(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    writeContract?: any
  ): Promise<BridgeResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.executeSolanaToEvm(amount, recipientAddress, nativeFee, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.executeEvmToSolana(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      case 'EVM_TO_EVM':
        return this.executeEvmToEvm(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  private async quoteSolanaToEvm(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet: any
  ): Promise<QuoteResult> {
    try {
      if (!solanaWallet?.publicKey) {
        throw new Error("Solana wallet not connected");
      }

      // Create UMI instance
      const rpcUrl = getNetworkRpcUrl('solana');
      const umi = createUmi(rpcUrl);
      umi.use(walletAdapterIdentity(solanaWallet));

      const mint = publicKey(CONTRACT_ADDRESSES.solana.mint!);
      const tokenDecimals = getTokenDecimals('solana');

      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, tokenDecimals)));

      // Convert recipient address to bytes32 format
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get the destination endpoint ID based on the target network
      const dstEid = this.config.toEndpointId;

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey.toString()),
          tokenMint: mint,
          tokenEscrow: publicKey(CONTRACT_ADDRESSES.solana.escrow!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32.replace('0x', ''), 'hex'),
          dstEid,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(CONTRACT_ADDRESSES.solana.program!),
        }
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
      };
    } catch (error) {
      console.error('Error getting Solana to EVM quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // SOL
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 9).toString(), // SOL has 9 decimals
        receiveAmount,
      };
    }
  }

  private async quoteEvmToSolana(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      // Get current gas prices
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453; // Base chain ID
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = gasLimit * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = chainId === 8453 ? BigInt(2 * 1e9) : BigInt(25 * 1e9); // 2 gwei for Base, 25 gwei for Ethereum
        nativeFee = gasLimit * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // EVM has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
        gasPrice: gasPrice?.maxFeePerGas.toString() || (chainId === 8453 ? "2000000000" : "25000000000"),
      };
    } catch (error) {
      console.error('Error getting EVM to Solana quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // ETH
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 18).toString(),
        receiveAmount,
        gasPrice: "20000000000", // 20 gwei
      };
    }
  }

  private async quoteEvmToEvm(
    amount: string,
    recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      // Get current gas prices for the source network
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453; // Base chain ID
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = gasLimit * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = chainId === 8453 ? BigInt(2 * 1e9) : BigInt(25 * 1e9); // 2 gwei for Base, 25 gwei for Ethereum
        nativeFee = gasLimit * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio with same decimals)
      // Both EVM networks use 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
        gasPrice: gasPrice?.maxFeePerGas.toString() || (chainId === 8453 ? "2000000000" : "25000000000"),
      };
    } catch (error) {
      console.error('Error getting EVM to EVM quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // ETH
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 18).toString(),
        receiveAmount,
        gasPrice: "25000000000", // 25 gwei
      };
    }
  }

  private async executeSolanaToEvm(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _solanaWallet: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual Solana OFT contract
    throw new Error("Solana to EVM bridging is not yet implemented");
  }

  private async executeEvmToSolana(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contract
    throw new Error("EVM to Solana bridging is not yet implemented");
  }

  private async executeEvmToEvm(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contracts
    throw new Error("EVM to EVM bridging is not yet implemented");
  }
}
