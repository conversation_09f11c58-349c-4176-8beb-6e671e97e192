import { parseUnits } from 'viem';
import { BridgeConfig, getBridgeConfig } from '../utils/bridgeConfig';
import { Network } from '../components/NetworkSelector';

export interface QuoteResult {
  nativeFee: string;
  receiveAmount: string;
  gasPrice?: string;
}

export interface BridgeResult {
  txHash: string;
  layerZeroScanLink?: string;
}

export class BridgeService {
  private config: BridgeConfig;

  constructor(fromNetwork: Network, toNetwork: Network) {
    this.config = getBridgeConfig(fromNetwork, toNetwork);
  }

  isSupported(): boolean {
    return this.config.isSupported;
  }

  getBridgeType(): string {
    return this.config.bridgeType;
  }

  async quoteBridge(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string
  ): Promise<QuoteResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.quoteSolanaToEvm(amount, recipientAddress, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.quoteEvmToSolana(amount, recipientAddress, ethAddress || '');
      case 'EVM_TO_EVM':
        return this.quoteEvmToEvm(amount, recipientAddress, ethAddress || '');
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  async executeBridge(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    writeContract?: any
  ): Promise<BridgeResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.executeSolanaToEvm(amount, recipientAddress, nativeFee, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.executeEvmToSolana(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      case 'EVM_TO_EVM':
        return this.executeEvmToEvm(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  private async quoteSolanaToEvm(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _solanaWallet: any
  ): Promise<QuoteResult> {




    // For now, return a mock quote for Solana to EVM
    // TODO: Fix the oft.quote function call with correct parameters
    const mockFee = "0.001"; // SOL
    const receiveAmount = parseFloat(amount).toFixed(6); // No fee deduction - user receives full amount

    return {
      nativeFee: parseUnits(mockFee, 9).toString(), // SOL has 9 decimals
      receiveAmount,
    };
  }

  private async quoteEvmToSolana(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string
  ): Promise<QuoteResult> {
    // For now, return a mock quote for EVM to Solana
    // This would need to be implemented with the actual EVM OFT contract
    const mockFee = "0.001"; // ETH
    const receiveAmount = parseFloat(amount).toFixed(6); // No fee deduction - user receives full amount

    return {
      nativeFee: parseUnits(mockFee, 18).toString(),
      receiveAmount,
      gasPrice: "20000000000", // 20 gwei
    };
  }

  private async quoteEvmToEvm(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string
  ): Promise<QuoteResult> {
    // For now, return a mock quote for EVM to EVM
    // This would need to be implemented with the actual EVM OFT contracts
    const mockFee = "0.001"; // ETH (reduced from 0.002 to 0.001 for consistency)
    const receiveAmount = parseFloat(amount).toFixed(6); // No fee deduction - user receives full amount

    return {
      nativeFee: parseUnits(mockFee, 18).toString(),
      receiveAmount,
      gasPrice: "25000000000", // 25 gwei
    };
  }

  private async executeSolanaToEvm(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _solanaWallet: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual Solana OFT contract
    throw new Error("Solana to EVM bridging is not yet implemented");
  }

  private async executeEvmToSolana(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contract
    throw new Error("EVM to Solana bridging is not yet implemented");
  }

  private async executeEvmToEvm(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
    _writeContract: any
  ): Promise<BridgeResult> {
    // This would be implemented with the actual EVM OFT contracts
    throw new Error("EVM to EVM bridging is not yet implemented");
  }
}
